#include "main.h"

// INT 有按键触发为高电平，无按键为低电平
uint8_t key_data[3] = {0};
uint16_t no_action_time = 0; // 按键无操作时间
uint8_t enter_sleep_time = 0;
uint8_t power_flag = 0;
uint16_t power_off_time = 0;
uint8_t alarm_flag_on = 0;
uint8_t countdown_last_value = 0;
uint8_t count_down_flag = 0;
uint8_t light_direction = 0;                            // 0表示上升，1表示下降
uint8_t ambient_light_before_sleep = KEY_LED_LEVEL_0;   // 进入睡眠前的环境灯状态
enum e_state time_state_before_sleep = STATE_REAL_TIME; // 进入睡眠前的时间状态
uint8_t alarm_wakeup_flag = 0;                          // 闹钟响起后触摸唤醒标志

uint8_t vcc_detect_flag = 0; // 1 开机 0 关机
static uint8_t vcc_detect_flag_last = 0;

key_t key;

void key_hw_init(void)
{
    SET_REG(PORTIDX, 5);
    SET_REG_BITS(PINMOD76, PINMOD7, PIN_MODE_OD_WAKEUP);
    KEY_INT = 1;

    SET_REG(PORTIDX, 1);
    SET_REG_BITS(PINMOD10, PINMOD0, PIN_MODE_OD_WAKEUP);
    VCC_DETECT = 1;

    SET_REG_BITS(INTE1, PCIE, 1); // 开启引脚变化中断

    SET_REG(PORTIDX, 1); // pwr
    SET_REG_BITS(PINMOD32, PINMOD3, PIN_MODE_PP);
    PWR_ON = 0;
}

void key_state_init(void)
{
    key.curr_value = 0;
    key.last_value = 0;
    key.key_do_value = 0;
    key.long_time = 0;
    key.interval_time = 0;
    key.click_count = 0;
    key.lock = 0;
    key.state = KEY_RELEASE_DETECTED;
    key.type = KEY_TYPE_NONE;
    light_direction = 1;
    ambient_light_before_sleep = KEY_LED_LEVEL_0;
    time_state_before_sleep = STATE_REAL_TIME;
    alarm_wakeup_flag = 0;
}

void power_on_exe(void)
{
    if (first_power_on)
        first_power_on = 0;
    no_action_time = 0;
    enter_sleep_time = 0;

    ble_pair_flag = 0;
    ble_reconnect_flag = 0;

    // 开机时清除闹钟和贪睡相关标志位，确保状态正确
    alarm_state = 0;
    snooze_state = 0;
    count_down_flag = 0;
    snooze_time_ended = 0;
    countdown_time_ended = 0;
    alarm_wakeup_flag = 0;
    alarm_auto_close_time = 0;
    alarm_auto_close_flag = 0;

    // 重置贪睡时间为默认值
    rtc.snooze_time = 5;
    last_snooze_time = 0;

    pwm_led_on_flag = 1;
    pwm_led_off_flag = 0;

    // 启动显示LED渐亮呼吸效果
    display_led_breath_on_flag = 1;
    display_led_breath_off_flag = 0;
    breath_display_on_cnt = 0;
    breath_display_on_duty = 0; // 从0开始渐亮

    time_state = STATE_REAL_TIME;
    time_state_before_sleep = STATE_REAL_TIME; // 重置睡眠前状态
    music_mode = MODE_IDE;
    led_other_flag = 0;
    alarm_set_flag = 0;
    power_flag = 0;
    PWR_ON = 1;
    key_data[0] = 0;
    key_data[1] = 0;
    key_data[2] = 0;
}

// 开机
void key_power_exe(void)
{
    if (key.type == KEY_TYPE_LONG)
    {
        if (time_state != STATE_POWER_OFF) // 关机
        {

            pwm_led_on_flag = 0;
            pwm_led_off_flag = 1;

            // 启动显示LED渐灭呼吸效果
            display_led_breath_on_flag = 0;
            display_led_breath_off_flag = 1;
            breath_display_off_cnt = 0;
            breath_display_off_duty = 100; // 从100开始渐灭

            ble_pair_flag = 0;
            ble_reconnect_flag = 0;

            no_action_time = 0;
            enter_sleep_time = 0;

            // 清除闹钟和贪睡相关标志位
            alarm_state = 0;
            snooze_state = 0;
            count_down_flag = 0;
            countdown_time_ended = 0;
            snooze_time_ended = 0;
            alarm_wakeup_flag = 0;
            alarm_auto_close_time = 0;
            alarm_auto_close_flag = 0;

            // 清除贪睡时间
            rtc.snooze_time = 5;
            last_snooze_time = 0;

            time_state = STATE_POWER_OFF;
            time_state_before_sleep = STATE_REAL_TIME; // 重置睡眠前状态
            music_mode = MODE_IDE;
            led_other_flag = 0;
            power_flag = 1;
            // LED_WHITE = 1; // 注释掉，让呼吸效果控制

            key_data[0] = 0x01;
            key_data[1] = 0x0b;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
    }

    if (key.type == KEY_TYPE_SHORT)
    {
        if (time_state != STATE_POWER_OFF)
        {
            if (time_state == STATE_SET_COUNT_DOWN)
            {
                rtc.countdown_time = 0;
            }

            ble_pair_flag = 0;
            ble_reconnect_flag = 0;

            time_state = STATE_REAL_TIME;
            music_mode = MODE_IDE;
            led_other_flag = 0;
            alarm_set_flag = 0;
            key_data[0] = 0x01;
            key_data[1] = 0x10;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
    }
}

// 音量加
void key_volume_up_exe(void)
{
    if (key.type == KEY_TYPE_SHORT || key.type == KEY_TYPE_LONG_KEEP || key.type == KEY_TYPE_LONG || key.type == KEY_TYPE_DOUBLE)
    {
        if (time_state == STATE_REAL_TIME || time_state == STATE_COUNT_DOWN) // 声音加
        {
            key_data[0] = 0x02;
            if (music_mode == MODE_IDE)
            {
                if (vol.idl_vol < MAX_VOLUME)
                {
                    vol.idl_vol++;
                }
                key_data[1] = vol.idl_vol;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
            else if (music_mode == MODE_BT)
            {
                if (vol.bt_vol < MAX_VOLUME)
                {
                    vol.bt_vol++;
                }
                key_data[1] = vol.bt_vol;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
            else if (music_mode == MODE_AUX)
            {
                if (vol.aux_vol < MAX_VOLUME)
                {
                    vol.aux_vol++;
                }
                key_data[1] = vol.aux_vol;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
            else if (music_mode == MODE_AMBIENT)
            {
                if (vol.ambient_vol < MAX_VOLUME)
                {
                    vol.ambient_vol++;
                }
                key_data[1] = vol.ambient_vol;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
        else if (time_state == STATE_SET_TIME_HOUR)
        {
            hour_inc(&rtc.hour);
        }
        else if (time_state == STATE_SET_TIME_MINUTE)
        {
            minute_inc(&rtc.minute);
        }
        else if (time_state == STATE_SET_ALARM_HOUR)
        {
            // 如果闹钟未设置，从00开始
            if (rtc.alarm_hour == ALARM_NOT_SET_HOUR && rtc.alarm_minute == ALARM_NOT_SET_MINUTE)
            {
                rtc.alarm_hour = 0;
                rtc.alarm_minute = 0;
            }
            hour_inc(&rtc.alarm_hour);
        }
        else if (time_state == STATE_SET_ALARM_MINUTE)
        {
            // 如果闹钟未设置，从00开始
            if (rtc.alarm_hour == ALARM_NOT_SET_HOUR && rtc.alarm_minute == ALARM_NOT_SET_MINUTE)
            {
                rtc.alarm_hour = 0;
                rtc.alarm_minute = 0;
            }
            minute_inc(&rtc.alarm_minute);
        }
        else if (time_state == STATE_SET_BT_NUM_THOUSAND)
        {
            if (bt_name_num_thousand < 9)
            {
                bt_name_num_thousand++;
            }
            else
            {
                bt_name_num_thousand = 0;
            }
        }
        else if (time_state == STATE_SET_BT_NUM_HUNDRED)
        {
            if (bt_name_num_hundred < 9)
            {
                bt_name_num_hundred++;
            }
            else
            {
                bt_name_num_hundred = 0;
            }
        }
        else if (time_state == STATE_SET_BT_NUM_TEN)
        {
            if (bt_name_num_ten < 9)
            {
                bt_name_num_ten++;
            }
            else
            {
                bt_name_num_ten = 0;
            }
        }
        else if (time_state == STATE_SET_BT_NUM_ONE)
        {
            if (bt_name_num_one < 9)
            {
                bt_name_num_one++;
            }
            else
            {
                bt_name_num_one = 0;
            }
        }
#if TIMER_SET_ALARM_ENABLE
        else if (time_state == STATE_SET_COUNT_DOWN)
        {
            change_countdown_time_inc();
        }
#endif
    }
}

// 音量减
void key_volume_down_exe(void)
{
    if (key.type == KEY_TYPE_SHORT || key.type == KEY_TYPE_LONG_KEEP || key.type == KEY_TYPE_LONG || key.type == KEY_TYPE_DOUBLE)
    {
        if (time_state == STATE_REAL_TIME || time_state == STATE_COUNT_DOWN) // 音量减
        {
            key_data[0] = 0x02;
            if (music_mode == MODE_IDE)
            {
                if (vol.idl_vol > 0)
                {
                    vol.idl_vol--;
                }
                key_data[1] = vol.idl_vol;
            }
            else if (music_mode == MODE_BT)
            {
                if (vol.bt_vol > 0)
                {
                    vol.bt_vol--;
                }
                key_data[1] = vol.bt_vol;
            }
            else if (music_mode == MODE_AUX)
            {
                if (vol.aux_vol > 0)
                {
                    vol.aux_vol--;
                }
                key_data[1] = vol.aux_vol;
            }
            else if (music_mode == MODE_AMBIENT)
            {
                if (vol.ambient_vol > 0)
                {
                    vol.ambient_vol--;
                }
                key_data[1] = vol.ambient_vol;
            }
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
        else if (time_state == STATE_SET_TIME_HOUR)
        {
            hour_dec(&rtc.hour);
        }
        else if (time_state == STATE_SET_TIME_MINUTE)
        {
            minute_dec(&rtc.minute);
        }
        else if (time_state == STATE_SET_ALARM_HOUR)
        {
            // 如果闹钟未设置，从00开始
            if (rtc.alarm_hour == ALARM_NOT_SET_HOUR && rtc.alarm_minute == ALARM_NOT_SET_MINUTE)
            {
                rtc.alarm_hour = 0;
                rtc.alarm_minute = 0;
            }
            hour_dec(&rtc.alarm_hour);
        }
        else if (time_state == STATE_SET_ALARM_MINUTE)
        {
            // 如果闹钟未设置，从00开始
            if (rtc.alarm_hour == ALARM_NOT_SET_HOUR && rtc.alarm_minute == ALARM_NOT_SET_MINUTE)
            {
                rtc.alarm_hour = 0;
                rtc.alarm_minute = 0;
            }
            minute_dec(&rtc.alarm_minute);
        }
        else if (time_state == STATE_SET_BT_NUM_THOUSAND)
        {
            if (bt_name_num_thousand > 0)
            {
                bt_name_num_thousand--;
            }
            else
            {
                bt_name_num_thousand = 9;
            }
        }
        else if (time_state == STATE_SET_BT_NUM_HUNDRED)
        {
            if (bt_name_num_hundred > 0)
            {
                bt_name_num_hundred--;
            }
            else
            {
                bt_name_num_hundred = 9;
            }
        }
        else if (time_state == STATE_SET_BT_NUM_TEN)
        {
            if (bt_name_num_ten > 0)
            {
                bt_name_num_ten--;
            }
            else
            {
                bt_name_num_ten = 9;
            }
        }
        else if (time_state == STATE_SET_BT_NUM_ONE)
        {
            if (bt_name_num_one > 0)
            {
                bt_name_num_one--;
            }
            else
            {
                bt_name_num_one = 9;
            }
        }
#if TIMER_SET_ALARM_ENABLE
        else if (time_state == STATE_SET_COUNT_DOWN)
        {
            change_countdown_time_dec();
        }
#endif
    }
}

// 播放暂停
void key_play_pause_exe(void)
{
    if (key.type == KEY_TYPE_SHORT)
    {
        if (time_state == STATE_REAL_TIME || time_state == STATE_COUNT_DOWN)
        {
            if (music_mode == MODE_BT || music_mode == MODE_AMBIENT) // 播放暂停
            {
                key_data[0] = 0x01;
                key_data[1] = 0x0c;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
            else if (music_mode == MODE_AUX) // 静音
            {
                key_data[1] = 0x09;
                key_data[0] = 0x01;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
        else if (time_state == STATE_SET_ALARM_HOUR)
        {
            time_state = STATE_SET_ALARM_MINUTE;
        }
        else if (time_state == STATE_SET_ALARM_MINUTE)
        {
            time_state = STATE_SET_ALARM_HOUR;
        }
        else if (time_state == STATE_SET_TIME_HOUR)
        {
            time_state = STATE_SET_TIME_MINUTE;
        }
        else if (time_state == STATE_SET_TIME_MINUTE)
        {
            time_state = STATE_SET_TIME_HOUR;
        }
        else if (time_state == STATE_SET_BT_NUM_THOUSAND)
        {
            time_state = STATE_SET_BT_NUM_HUNDRED;
        }
        else if (time_state == STATE_SET_BT_NUM_HUNDRED)
        {
            time_state = STATE_SET_BT_NUM_TEN;
        }
        else if (time_state == STATE_SET_BT_NUM_TEN)
        {
            time_state = STATE_SET_BT_NUM_ONE;
        }
        else if (time_state == STATE_SET_BT_NUM_ONE)
        {
            time_state = STATE_SET_BT_NUM_THOUSAND;
        }
    }
    else if (key.type == KEY_TYPE_DOUBLE)
    {
        if (time_state == STATE_REAL_TIME || time_state == STATE_COUNT_DOWN)
        {
            if (music_mode == MODE_BT) // 下一曲
            {
                key_data[0] = 0x01;
                key_data[1] = 0x0d;
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
    }
}

// 蓝牙
void key_bt_exe(void)
{
    if (key.type == KEY_TYPE_SHORT && music_mode != MODE_BT) // 进入蓝牙回连
    {
        ble_pair_flag = 0;
        ble_reconnect_flag = 1;
        music_mode = MODE_BT_RECONNECT;

        key_data[0] = 0x01;
        key_data[1] = 0x06;
        uart0_send(key_data, 4);
        key_data[0] = 0;
        key_data[1] = 0;
    }
    else if (key.type == KEY_TYPE_LONG) // 进入配对
    {
        ble_pair_flag = 1;
        ble_reconnect_flag = 0;
        music_mode = MODE_BT_PAIR;
        time_state = STATE_BT_NUM;

        // 设置LED_WHITE呼吸效果的最大亮度为当前环境灯亮度
        set_white_duty_config_by_ambient_light();

        key_data[0] = 0x01;
        key_data[1] = 0x07;
        uart0_send(key_data, 4);
        key_data[0] = 0;
        key_data[1] = 0;
    }
}

// 白噪音
void key_music_exe(void)
{
    if (key.type == KEY_TYPE_SHORT) // 切换睡眠模式
    {
        if (music_mode != MODE_AMBIENT) // 进入睡眠模式
        {
            ble_pair_flag = 0;
            ble_reconnect_flag = 0;

            music_mode = MODE_AMBIENT;
            if (time_state == STATE_BT_NUM)
                time_state = STATE_REAL_TIME;

            key_data[0] = 0x01;
            key_data[1] = 0x03;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
        else if (music_mode == MODE_AMBIENT) // 切换睡眠音乐
        {
            key_data[0] = 0x01;
            key_data[1] = 0x04;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
    }
}

// 倒计时 Timer

#if TIMER_SET_ALARM_ENABLE
void key_timer_exe(void)
{
    if (key.type == KEY_TYPE_SHORT) // 设置闹钟
    {
        if (snooze_state)
        {
            if (time_state == STATE_SNOOZE_CNT)
            {
                time_state = STATE_REAL_TIME;
            }
            else if (time_state == STATE_REAL_TIME)
            {
                time_state = STATE_SNOOZE_CNT;
            }
        }
        else
        {
            time_state = STATE_SET_ALARM_HOUR;
            alarm_set_flag = 1;
            alarm_triggered_today = 0;
        }
    }

    if (key.type == KEY_TYPE_LONG)
    {
        rtc.countdown_time = 0;
        time_state = STATE_SET_COUNT_DOWN;
    }
}
#else
void key_timer_exe(void)
{
    if (key.type == KEY_TYPE_SHORT) // 设置倒计时
    {
        if (count_down_flag)
        {
            time_state = STATE_COUNT_DOWN;
        }
        else if (!count_down_flag && time_state != STATE_SET_COUNT_DOWN)
        {
            rtc.countdown_time = 0;
            time_state = STATE_SET_COUNT_DOWN;
        }
        else if (time_state == STATE_SET_COUNT_DOWN)
        {
            change_countdown_time_inc();
        }
    }

    if (key.type == KEY_TYPE_LONG) // 长按关闭倒计时
    {
        count_down_flag = 0;
        rtc.countdown_time = 0;
    }
}
#endif

// 环境灯
void key_amb_exe(void)
{
    if (key.type == KEY_TYPE_SHORT) // 调节环境灯亮度
    {
        if (light_direction == 0) // 亮度上升阶段
        {
            if (key_led_light == KEY_LED_LEVEL_1 && ambient_light == KEY_LED_LEVEL_0)
            {
                key_led_light = KEY_LED_LEVEL_1;
                ambient_light = KEY_LED_LEVEL_1;
                white_duty_config = 25;
            }
            else if (key_led_light == KEY_LED_LEVEL_1 && ambient_light == KEY_LED_LEVEL_1)
            {
                key_led_light = KEY_LED_LEVEL_2;
                ambient_light = KEY_LED_LEVEL_2;
                white_duty_config = 50;
            }
            else if (key_led_light == KEY_LED_LEVEL_2 && ambient_light == KEY_LED_LEVEL_2)
            {
                key_led_light = KEY_LED_LEVEL_3;
                ambient_light = KEY_LED_LEVEL_3;
                white_duty_config = 75;
            }
            else if (key_led_light == KEY_LED_LEVEL_3 && ambient_light == KEY_LED_LEVEL_3)
            {
                key_led_light = KEY_LED_LEVEL_4;
                ambient_light = KEY_LED_LEVEL_4;
                white_duty_config = 100;
                light_direction = 1; // 达到最高亮度，切换为下降方向
            }
        }
        else if (light_direction == 1) // 亮度下降阶段
        {
            if (key_led_light == KEY_LED_LEVEL_4 && ambient_light == KEY_LED_LEVEL_4)
            {
                key_led_light = KEY_LED_LEVEL_3;
                ambient_light = KEY_LED_LEVEL_3;
                white_duty_config = 75;
            }
            else if (key_led_light == KEY_LED_LEVEL_3 && ambient_light == KEY_LED_LEVEL_3)
            {
                key_led_light = KEY_LED_LEVEL_2;
                ambient_light = KEY_LED_LEVEL_2;
                white_duty_config = 50;
            }
            else if (key_led_light == KEY_LED_LEVEL_2 && ambient_light == KEY_LED_LEVEL_2)
            {
                key_led_light = KEY_LED_LEVEL_1;
                ambient_light = KEY_LED_LEVEL_1;
                white_duty_config = 25;
            }
            else if (key_led_light == KEY_LED_LEVEL_1 && ambient_light == KEY_LED_LEVEL_1)
            {
                light_direction = 0; // 达到最低亮度，切换为上升方向
                key_led_light = KEY_LED_LEVEL_1;
                ambient_light = KEY_LED_LEVEL_0;
                white_duty_config = 0;
            }
        }
    }
    else if (key.type == KEY_TYPE_LONG)
    {
        time_state = STATE_SET_BT_NUM_THOUSAND;
    }
}

// AUX
void key_aux_exe(void)
{
    if (key.type == KEY_TYPE_SHORT) // 切换AUX模式
    {
        ble_pair_flag = 0;
        ble_reconnect_flag = 0;

        music_mode = MODE_AUX;
        if (time_state == STATE_BT_NUM)
            time_state = STATE_REAL_TIME;

        key_data[1] = 0x08;
        key_data[0] = 0x01;
        uart0_send(key_data, 4);
        key_data[0] = 0;
        key_data[1] = 0;
    }
}

// 闹钟
void key_snooze_exe(void)
{
    uint16_t current_time = 0; // 当前时间
    uint16_t alarm_time = 0;
    uint16_t time_diff = 0;

    if (key.type == KEY_TYPE_SHORT)
    {
        if (alarm_state)
        {

            alarm_wakeup_flag = 0; // 清除闹钟唤醒标志
            alarm_state = 0;
            snooze_state = 0;
            alarm_auto_close_time = 0; // 清除自动关闭计时器
            alarm_auto_close_flag = 0; // 清除自动关闭标志

            // 重置睡眠计时器，闹钟结束后按正常逻辑40s无操作进入睡眠
            no_action_time = 0;
            enter_sleep_time = 0;

            // 发送关闭闹钟信号
            key_data[0] = 0x01;
            key_data[1] = 0x02;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;

            current_time = rtc.hour * 60 + rtc.minute;
            alarm_time = rtc.alarm_hour * 60 + rtc.alarm_minute;
            if (current_time >= alarm_time)
            {
                time_diff = current_time - alarm_time;
            }
            else
            {
                time_diff = (24 * 60 - alarm_time) + current_time;
            }

            if (time_diff <= 120)
            {
                time_state = STATE_SET_SNOOZE_CNT;

                // 恢复上一次设置的贪睡时间（如果有记录的话）
                if (last_snooze_time != 0)
                {
                    rtc.snooze_time = last_snooze_time;
                }
            }
            else
            {
                if (time_state == STATE_SNOOZE_CNT)
                {
                    time_state = STATE_REAL_TIME;
                }
            }
        }
        else if (time_state == STATE_SET_SNOOZE_CNT)
        {
            change_snooze_time();
        }
    }
    else if (key.type == KEY_TYPE_LONG) // 关闭闹钟
    {
        if (alarm_state)
        {
            alarm_wakeup_flag = 0; // 清除闹钟唤醒标志

            alarm_state = 0;
            snooze_state = 0;
            alarm_auto_close_time = 0; // 清除自动关闭计时器
            alarm_auto_close_flag = 0; // 清除自动关闭标志
            alarm_triggered_today = 1; // 彻底关闭今天的闹钟，防止再次进入贪睡设置

            // 重置睡眠计时器，闹钟结束后按正常逻辑40s无操作进入睡眠
            no_action_time = 0;
            enter_sleep_time = 0;

            // 发送关闭闹钟信号
            key_data[0] = 0x01;
            key_data[1] = 0x02;
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;

            time_state = STATE_REAL_TIME;
        }
    }
}

void key_exe(void)
{
    if (key.type == KEY_TYPE_NONE)
    {
        return;
    }

    // 检查按键值是否为允许的按键值
    if (key.key_do_value != KEY_VOLUME_DOWN &&
        key.key_do_value != KEY_AUX &&
        key.key_do_value != KEY_POWER &&
        key.key_do_value != KEY_PLAY_PAUSE &&
        key.key_do_value != KEY_AMB &&
        key.key_do_value != KEY_TIMER &&
        key.key_do_value != KEY_MUSIC &&
        key.key_do_value != KEY_BT &&
        key.key_do_value != KEY_VOLUME_UP &&
        key.key_do_value != KEY_SNOOZE)
    {
        key.type = KEY_TYPE_NONE;
        key.key_do_value = 0;
        return;
    }

    if (time_state == STATE_SLEEP)
    {
        if (key.key_do_value != 0)
        {
            // 如果在贪睡倒计时状态下唤醒，直接显示贪睡倒计时剩余时间
            if (snooze_state)
            {
                time_state = STATE_SNOOZE_CNT;
            }
            else
            {
                // 恢复到睡眠前的状态，而不是强制回到实时时钟
                time_state = time_state_before_sleep;
            }

            pwm_led_on_flag = 1;
            pwm_led_off_flag = 0;

            // 启动显示LED渐亮呼吸效果
            display_led_breath_on_flag = 1;
            display_led_breath_off_flag = 0;
            breath_display_on_cnt = 0;
            breath_display_on_duty = 0; // 从0开始渐亮

            // 如果进入睡眠前led_white是关的，唤醒时设为KEY_LED_LEVEL_1
            if (ambient_light_before_sleep == KEY_LED_LEVEL_0)
            {
                ambient_light = KEY_LED_LEVEL_1;
                key_led_light = KEY_LED_LEVEL_1; // 同步按键LED亮度
                white_duty_config = 25;          // 设置为KEY_LED_LEVEL_1对应的亮度
            }
            else
            {
                // 如果进入睡眠前led_white是开的，恢复原来的状态
                ambient_light = ambient_light_before_sleep;
                key_led_light = ambient_light_before_sleep; // 同步按键LED亮度
                set_white_duty_config_by_ambient_light();
            }
        }
        key.type = KEY_TYPE_NONE;
        key.key_do_value = 0;
        return;
    }

    if (time_state == STATE_POWER_OFF)
    {
        if (key.key_do_value != 0)
        {
            power_on_exe();
        }
        key.type = KEY_TYPE_NONE;
        key.key_do_value = 0;
        return;
    }

    // 在各种设置状态下屏蔽不相关的按键，避免状态冲突
    if (time_state == STATE_SET_ALARM_HOUR || time_state == STATE_SET_ALARM_MINUTE)
    {
        // 设置闹钟时间状态：只允许音量键调整时间、播放暂停键切换时分、电源键
        if (key.key_do_value != KEY_VOLUME_UP &&
            key.key_do_value != KEY_VOLUME_DOWN &&
            key.key_do_value != KEY_PLAY_PAUSE &&
            key.key_do_value != KEY_POWER)
        {
            key.type = KEY_TYPE_NONE;
            key.key_do_value = 0;
            return;
        }
    }
    else if (time_state == STATE_SET_TIME_HOUR || time_state == STATE_SET_TIME_MINUTE)
    {
        // 设置时间状态：只允许音量键调整时间、播放暂停键切换时分、电源键
        if (key.key_do_value != KEY_VOLUME_UP &&
            key.key_do_value != KEY_VOLUME_DOWN &&
            key.key_do_value != KEY_PLAY_PAUSE &&
            key.key_do_value != KEY_POWER)
        {
            key.type = KEY_TYPE_NONE;
            key.key_do_value = 0;
            return;
        }
    }
    else if (time_state == STATE_SET_SNOOZE_CNT)
    {
        // 设置贪睡时间状态：只允许贪睡键调整时间、电源键
        if (key.key_do_value != KEY_SNOOZE)
        {
            key.type = KEY_TYPE_NONE;
            key.key_do_value = 0;
            return;
        }
    }
    else if (time_state == STATE_SET_COUNT_DOWN)
    {
        // 设置倒计时状态：只允许音量键调整时间、电源键
        if (key.key_do_value != KEY_VOLUME_UP &&
            key.key_do_value != KEY_VOLUME_DOWN &&
            key.key_do_value != KEY_POWER)
        {
            key.type = KEY_TYPE_NONE;
            key.key_do_value = 0;
            return;
        }
    }
    else if (time_state == STATE_SET_BT_NUM_THOUSAND ||
             time_state == STATE_SET_BT_NUM_HUNDRED ||
             time_state == STATE_SET_BT_NUM_TEN ||
             time_state == STATE_SET_BT_NUM_ONE)
    {
        // 设置蓝牙编号状态：只允许音量键调整数字、播放暂停键切换位数、电源键
        if (key.key_do_value != KEY_VOLUME_UP &&
            key.key_do_value != KEY_VOLUME_DOWN &&
            key.key_do_value != KEY_PLAY_PAUSE &&
            key.key_do_value != KEY_POWER)
        {
            key.type = KEY_TYPE_NONE;
            key.key_do_value = 0;
            return;
        }
    }

    if (key.key_do_value == KEY_POWER)
    {
        key_power_exe();
        key.key_do_value = 0;
    }

    if (key.key_do_value == KEY_VOLUME_UP)
    {
        key_volume_up_exe();
        if (key.type == KEY_TYPE_LONG_KEEP || key.type == KEY_TYPE_LONG)
        {
            key.type = KEY_TYPE_NONE;
            return;
        }
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_VOLUME_DOWN)
    {
        key_volume_down_exe();
        if (key.type == KEY_TYPE_LONG_KEEP || key.type == KEY_TYPE_LONG)
        {
            key.type = KEY_TYPE_NONE;
            return;
        }
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_PLAY_PAUSE)
    {
        key_play_pause_exe();
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_BT)
    {
        key_bt_exe();
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_MUSIC)
    {
        key_music_exe();
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_TIMER)
    {
        key_timer_exe();
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_AMB)
    {
        key_amb_exe();
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_AUX)
    {
        key_aux_exe();
        key.key_do_value = 0;
    }
    if (key.key_do_value == KEY_SNOOZE)
    {
        key_snooze_exe();
        key.key_do_value = 0;
    }
    key.type = KEY_TYPE_NONE;
}

void key_time_set_check(void)
{
    // 在关机状态下不进行任何检查
    if (time_state == STATE_POWER_OFF)
    {
        no_action_time = 0;
        enter_sleep_time = 0;
        return;
    }

    // If key value changed, reset timer
    if (key.key_do_value != 0)
    {
        no_action_time = 0;
        enter_sleep_time = 0;
    }
    // Otherwise increment timer
    else
    {
        no_action_time++;
        // After 5 seconds (5000 * 1ms = 5s) of no action
        if (no_action_time >= 5000)
        {
            if (time_state == STATE_REAL_TIME || time_state == STATE_SNOOZE_CNT || time_state == STATE_COUNT_DOWN)
            {
                // 如果闹钟正在响铃，不进入睡眠状态
                if (alarm_state)
                {
                    enter_sleep_time = 0; // 重置睡眠计时器
                }
                else
                {
                    enter_sleep_time++;
                    if (enter_sleep_time >= 8) // 40秒后进入睡眠模式
                    {
                        // 保存进入睡眠前的环境灯状态和时间状态
                        ambient_light_before_sleep = ambient_light;
                        time_state_before_sleep = time_state;

                        time_state = STATE_SLEEP;
                        pwm_led_on_flag = 0;
                        pwm_led_off_flag = 1;

                        // 启动显示LED渐灭呼吸效果
                        display_led_breath_on_flag = 0;
                        display_led_breath_off_flag = 1;
                        breath_display_off_cnt = 0;
                        breath_display_off_duty = 100; // 从100开始渐灭

                        enter_sleep_time = 0;
                    }
                }
            }
            else
            {
                enter_sleep_time = 0;
            }

            no_action_time = 0;
            if (time_state == STATE_SET_TIME_HOUR || time_state == STATE_SET_TIME_MINUTE)
            {
                rtc.second = 0; // 确认时间设置时清零秒数
                time_state = STATE_REAL_TIME;
                alarm_set_flag = 0;
            }
            else if (time_state == STATE_SET_ALARM_HOUR || time_state == STATE_SET_ALARM_MINUTE)
            {
                time_state = STATE_REAL_TIME;
                alarm_set_flag = 0;
            }
            else if (time_state == STATE_SET_COUNT_DOWN)
            {
                if (rtc.countdown_time == 0)
                {
                    time_state = STATE_REAL_TIME;
                    count_down_flag = 0;
                }
                else
                {
                    time_state = STATE_COUNT_DOWN;
                    countdown_last_value = rtc.countdown_time;
                    countdown_time_ended = 0;
                    count_down_flag = 1;
                }
            }
            else if (time_state == STATE_SET_SNOOZE_CNT)
            {
                // 5秒后确认当前贪睡时间并开始贪睡倒计时
                // 记录确认的贪睡时间
                last_snooze_time = rtc.snooze_time;

                // 开始贪睡倒计时
                time_state = STATE_SNOOZE_CNT;
                snooze_state = 1;

                // 清除贪睡时间结束标志，避免立即触发闹钟
                snooze_time_ended = 0;
            }
            else if (time_state == STATE_SET_BT_NUM_TEN || time_state == STATE_SET_BT_NUM_ONE || time_state == STATE_SET_BT_NUM_THOUSAND || time_state == STATE_SET_BT_NUM_HUNDRED)
            {
                time_state = STATE_REAL_TIME;
                key_data[0] = 0x05;
                key_data[1] = bt_name_num_thousand * 10 + bt_name_num_hundred;
                key_data[2] = bt_name_num_ten * 10 + bt_name_num_one;
                uart0_send(key_data, 5);
                key_data[0] = 0;
                key_data[1] = 0;
                key_data[2] = 0;
            }
        }
    }
}

void vcc_detect_task(void)
{
    if (vcc_detect_flag != vcc_detect_flag_last)
    {
        vcc_detect_flag_last = vcc_detect_flag;

        if (vcc_detect_flag) // 插电开机
        {
            // 只有在关机状态下才执行开机操作
            if (time_state == STATE_POWER_OFF)
            {
                power_on_exe(); // 调用开机执行函数

                // 发送开机信号
                key_data[0] = 0x01;
                key_data[1] = 0x0a; // 开机信号
                uart0_send(key_data, 4);
                key_data[0] = 0;
                key_data[1] = 0;
            }
        }
        else if (!vcc_detect_flag) // 断电关机
        {

            ble_pair_flag = 0;
            ble_reconnect_flag = 0;

            // 清除闹钟和贪睡相关标志位
            alarm_state = 0;
            snooze_state = 0;
            countdown_time_ended = 0;
            count_down_flag = 0;
            snooze_time_ended = 0;
            alarm_auto_close_time = 0;
            alarm_auto_close_flag = 0;

            // 清除贪睡时间
            rtc.snooze_time = 5;
            last_snooze_time = 0;

            // 启动显示LED渐灭呼吸效果
            display_led_breath_on_flag = 0;
            display_led_breath_off_flag = 1;
            breath_display_off_cnt = 0;
            breath_display_off_duty = 100; // 从100开始渐灭

            time_state = STATE_POWER_OFF;
            time_state_before_sleep = STATE_REAL_TIME; // 重置睡眠前状态
            music_mode = MODE_IDE;
            led_other_flag = 0;
            power_flag = 1;
            // LED_WHITE = 1; // 注释掉，让呼吸效果控制

            key_data[0] = 0x01;
            key_data[1] = 0x0b; // 关机信号
            uart0_send(key_data, 4);
            key_data[0] = 0;
            key_data[1] = 0;
        }
    }
}

void key_task(void)
{
    if (scan_key_time >= 10) // 1ms
    {
        scan_key_time = 0;
        key_iic_driver();
        key_time_set_check();
        show_led_state();
        vcc_detect_task();
        key_exe();

        if (power_flag)
        {
            power_off_time++;
            if (power_off_time >= 4000)
            {
                power_flag = 0;
                power_off_time = 0;
                PWR_ON = 0;
            }
        }
        else
        {
            power_off_time = 0;
        }
    }
}
